#!/usr/bin/env python3
"""
Test script for Phase 1 implementation of embedding and retrieval system optimization.

This script tests the four main components implemented in Phase 1:
1. Centralized Text Splitting Service
2. Embedding Caching Implementation
3. Vector Database Initialization Optimization
4. Chunk Size Standardization
"""

import os
import sys
import logging
from typing import List

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_text_splitter_service():
    """Test the centralized text splitter service."""
    logger.info("Testing Text Splitter Service...")
    
    try:
        from app.services.text_splitter_service import (
            get_text_splitter_service, 
            get_text_splitter,
            split_text_chunks,
            split_document_chunks
        )
        from langchain.schema import Document
        
        # Test service initialization
        service = get_text_splitter_service()
        logger.info(f"✓ Text splitter service initialized successfully")
        
        # Test configuration loading
        config = service.get_config()
        logger.info(f"✓ Configuration loaded: chunk_size={config.get('chunk_size')}, chunk_overlap={config.get('chunk_overlap')}")
        
        # Test different document types
        for doc_type in ["default", "web", "academic", "pdf"]:
            splitter = get_text_splitter(doc_type)
            chunk_info = service.get_chunk_info(doc_type)
            logger.info(f"✓ {doc_type} splitter: chunk_size={chunk_info['chunk_size']}, overlap={chunk_info['chunk_overlap']}")
        
        # Test text splitting
        test_text = "This is a test document. " * 100  # Create a longer text
        chunks = split_text_chunks(test_text, "default")
        logger.info(f"✓ Text splitting: {len(chunks)} chunks created from test text")
        
        # Test document splitting
        test_doc = Document(page_content=test_text, metadata={"source": "test"})
        doc_chunks = split_document_chunks([test_doc], "pdf")
        logger.info(f"✓ Document splitting: {len(doc_chunks)} chunks created from test document")
        
        logger.info("✅ Text Splitter Service tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Text Splitter Service test failed: {e}")
        return False

def test_embedding_cache():
    """Test the embedding caching service."""
    logger.info("Testing Embedding Cache Service...")
    
    try:
        from app.services.embedding_cache import (
            get_embedding_cache,
            get_cached_embedding,
            cache_embedding
        )
        
        # Test cache initialization
        cache = get_embedding_cache()
        logger.info(f"✓ Embedding cache initialized successfully")
        
        # Test cache statistics
        stats = cache.get_cache_stats()
        logger.info(f"✓ Cache stats: {stats}")
        
        # Test caching and retrieval
        test_model = "test-model"
        test_text = "This is a test text for embedding caching."
        test_embedding = [0.1, 0.2, 0.3, 0.4, 0.5]  # Mock embedding
        
        # Cache the embedding
        cache_embedding(test_model, test_text, test_embedding)
        logger.info(f"✓ Embedding cached successfully")
        
        # Retrieve from cache
        cached_result = get_cached_embedding(test_model, test_text)
        if cached_result == test_embedding:
            logger.info(f"✓ Embedding retrieved from cache successfully")
        else:
            logger.error(f"❌ Cache retrieval failed: expected {test_embedding}, got {cached_result}")
            return False
        
        # Test cache miss
        miss_result = get_cached_embedding("different-model", test_text)
        if miss_result is None:
            logger.info(f"✓ Cache miss handled correctly")
        else:
            logger.error(f"❌ Cache miss not handled correctly")
            return False
        
        # Test cache cleanup
        cleaned = cache.cleanup_expired_cache()
        logger.info(f"✓ Cache cleanup completed: {cleaned} files cleaned")
        
        logger.info("✅ Embedding Cache Service tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Embedding Cache Service test failed: {e}")
        return False

def test_optimized_vector_db():
    """Test the optimized vector database service."""
    logger.info("Testing Optimized Vector DB Service...")
    
    try:
        from app.services.optimized_vector_db import (
            get_optimized_vector_db,
            get_vector_db
        )
        
        # Test service initialization
        service = get_optimized_vector_db()
        logger.info(f"✓ Optimized vector DB service initialized successfully")
        
        # Test cache statistics
        stats = service.get_cache_stats()
        logger.info(f"✓ Vector DB cache stats: {stats}")
        
        # Test configuration reload
        service.reload_config()
        logger.info(f"✓ Configuration reloaded successfully")
        
        # Note: We can't test actual database operations without Ollama running
        # But we can test the service structure
        logger.info("✓ Optimized Vector DB service structure verified")
        
        logger.info("✅ Optimized Vector DB Service tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Optimized Vector DB Service test failed: {e}")
        return False

def test_configuration_consistency():
    """Test that all components use consistent configuration."""
    logger.info("Testing Configuration Consistency...")
    
    try:
        from app.services.text_splitter_service import get_text_splitter_service
        
        service = get_text_splitter_service()
        
        # Test that default configuration matches expected values
        default_info = service.get_chunk_info("default")
        pdf_info = service.get_chunk_info("pdf")
        
        expected_chunk_size = 800
        expected_overlap = 250
        
        if default_info['chunk_size'] == expected_chunk_size and default_info['chunk_overlap'] == expected_overlap:
            logger.info(f"✓ Default configuration correct: {default_info}")
        else:
            logger.error(f"❌ Default configuration incorrect: {default_info}")
            return False
        
        if pdf_info['chunk_size'] == expected_chunk_size and pdf_info['chunk_overlap'] == expected_overlap:
            logger.info(f"✓ PDF configuration correct: {pdf_info}")
        else:
            logger.error(f"❌ PDF configuration incorrect: {pdf_info}")
            return False
        
        # Test web configuration (should be smaller)
        web_info = service.get_chunk_info("web")
        if web_info['chunk_size'] <= expected_chunk_size:
            logger.info(f"✓ Web configuration appropriate: {web_info}")
        else:
            logger.error(f"❌ Web configuration too large: {web_info}")
            return False
        
        logger.info("✅ Configuration Consistency tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Configuration Consistency test failed: {e}")
        return False

def main():
    """Run all Phase 1 tests."""
    logger.info("🚀 Starting Phase 1 Implementation Tests...")
    
    tests = [
        ("Text Splitter Service", test_text_splitter_service),
        ("Embedding Cache", test_embedding_cache),
        ("Optimized Vector DB", test_optimized_vector_db),
        ("Configuration Consistency", test_configuration_consistency)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running {test_name} Test")
        logger.info(f"{'='*50}")
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All Phase 1 implementation tests passed!")
        return 0
    else:
        logger.error(f"💥 {total - passed} tests failed!")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
