"""
Centralized Text Splitting Service

This module provides a centralized service for text splitting across all document processing
components, eliminating code duplication and ensuring consistent chunking parameters.
"""

import os
import json
import logging
from typing import List, Dict, Any, Optional
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TextSplitterService:
    """
    Centralized service for text splitting with configurable parameters.
    
    This service consolidates text splitting logic that was previously duplicated
    across multiple components and provides document-type-specific configurations.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the TextSplitterService.
        
        Args:
            config_path: Path to configuration file. If None, uses default config.
        """
        self.config_path = config_path or os.path.join(
            os.path.dirname(__file__), '../../config/default_models.json'
        )
        self.config = self._load_config()
        self._splitters: Dict[str, RecursiveCharacterTextSplitter] = {}
        
        logger.info(f"TextSplitterService initialized with config from {self.config_path}")
    
    def _load_config(self) -> Dict[str, Any]:
        """
        Load chunking configuration from file.
        
        Returns:
            Dictionary containing embedding parameters configuration
        """
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                embedding_params = config.get('embedding_parameters', {})
                logger.info(f"Loaded embedding parameters from config: {embedding_params}")
                return embedding_params
            else:
                logger.warning(f"Config file not found at {self.config_path}, using defaults")
        except Exception as e:
            logger.error(f"Failed to load config from {self.config_path}: {e}")
        
        # Default configuration if file loading fails
        default_config = {
            "chunk_size": 800,
            "chunk_overlap": 250,
            "separators": ["\n\n", "\n", " ", ""]
        }
        logger.info(f"Using default embedding parameters: {default_config}")
        return default_config
    
    def get_splitter(self, document_type: str = "default") -> RecursiveCharacterTextSplitter:
        """
        Get or create a text splitter for the specified document type.
        
        Args:
            document_type: Type of document ('default', 'web', 'academic', 'pdf')
            
        Returns:
            RecursiveCharacterTextSplitter configured for the document type
        """
        if document_type not in self._splitters:
            config = self.config.copy()
            
            # Adjust parameters based on document type
            if document_type == "web":
                # Smaller chunks for web content to handle varied formatting
                config["chunk_size"] = min(600, config.get("chunk_size", 800))
                config["chunk_overlap"] = min(150, config.get("chunk_overlap", 250))
                logger.debug(f"Configured web splitter: chunk_size={config['chunk_size']}, overlap={config['chunk_overlap']}")
                
            elif document_type == "academic":
                # Larger chunks for academic papers to preserve context
                config["chunk_size"] = max(1000, config.get("chunk_size", 800))
                config["chunk_overlap"] = max(300, config.get("chunk_overlap", 250))
                logger.debug(f"Configured academic splitter: chunk_size={config['chunk_size']}, overlap={config['chunk_overlap']}")
                
            elif document_type == "pdf":
                # Standard configuration for PDF documents
                config["chunk_size"] = config.get("chunk_size", 800)
                config["chunk_overlap"] = config.get("chunk_overlap", 250)
                logger.debug(f"Configured PDF splitter: chunk_size={config['chunk_size']}, overlap={config['chunk_overlap']}")
                
            else:  # default
                # Use configuration as-is for default type
                config["chunk_size"] = config.get("chunk_size", 800)
                config["chunk_overlap"] = config.get("chunk_overlap", 250)
                logger.debug(f"Configured default splitter: chunk_size={config['chunk_size']}, overlap={config['chunk_overlap']}")
            
            # Create the splitter with configured parameters
            self._splitters[document_type] = RecursiveCharacterTextSplitter(
                chunk_size=config["chunk_size"],
                chunk_overlap=config["chunk_overlap"],
                separators=config.get("separators", ["\n\n", "\n", " ", ""])
            )
            
            logger.info(f"Created text splitter for document type '{document_type}' with "
                       f"chunk_size={config['chunk_size']}, chunk_overlap={config['chunk_overlap']}")
        
        return self._splitters[document_type]
    
    def split_text(self, text: str, document_type: str = "default") -> List[str]:
        """
        Split text into chunks using the appropriate splitter.
        
        Args:
            text: Text to split
            document_type: Type of document for appropriate chunking strategy
            
        Returns:
            List of text chunks
        """
        if not text or not text.strip():
            logger.warning("Empty or whitespace-only text provided for splitting")
            return []
        
        splitter = self.get_splitter(document_type)
        chunks = splitter.split_text(text)
        
        logger.debug(f"Split text into {len(chunks)} chunks using '{document_type}' splitter")
        return chunks
    
    def split_documents(self, documents: List[Document], document_type: str = "default") -> List[Document]:
        """
        Split documents into chunks using the appropriate splitter.
        
        Args:
            documents: List of Document objects to split
            document_type: Type of document for appropriate chunking strategy
            
        Returns:
            List of Document chunks
        """
        if not documents:
            logger.warning("Empty document list provided for splitting")
            return []
        
        splitter = self.get_splitter(document_type)
        chunks = splitter.split_documents(documents)
        
        logger.info(f"Split {len(documents)} documents into {len(chunks)} chunks using '{document_type}' splitter")
        return chunks
    
    def get_config(self) -> Dict[str, Any]:
        """
        Get current configuration.
        
        Returns:
            Current embedding parameters configuration
        """
        return self.config.copy()
    
    def reload_config(self) -> None:
        """
        Reload configuration from file and clear cached splitters.
        """
        logger.info("Reloading text splitter configuration")
        self.config = self._load_config()
        self._splitters.clear()
        logger.info("Configuration reloaded and splitter cache cleared")
    
    def get_chunk_info(self, document_type: str = "default") -> Dict[str, int]:
        """
        Get chunk size and overlap information for a document type.
        
        Args:
            document_type: Type of document
            
        Returns:
            Dictionary with chunk_size and chunk_overlap values
        """
        splitter = self.get_splitter(document_type)
        return {
            "chunk_size": splitter._chunk_size,
            "chunk_overlap": splitter._chunk_overlap
        }


# Global instance for use throughout the application
text_splitter_service = TextSplitterService()


def get_text_splitter_service() -> TextSplitterService:
    """
    Get the global TextSplitterService instance.
    
    Returns:
        Global TextSplitterService instance
    """
    return text_splitter_service


# Convenience functions for backward compatibility
def get_text_splitter(document_type: str = "default") -> RecursiveCharacterTextSplitter:
    """
    Get a text splitter for the specified document type.
    
    Args:
        document_type: Type of document ('default', 'web', 'academic', 'pdf')
        
    Returns:
        RecursiveCharacterTextSplitter configured for the document type
    """
    return text_splitter_service.get_splitter(document_type)


def split_text_chunks(text: str, document_type: str = "default") -> List[str]:
    """
    Split text into chunks using the centralized service.
    
    Args:
        text: Text to split
        document_type: Type of document for appropriate chunking strategy
        
    Returns:
        List of text chunks
    """
    return text_splitter_service.split_text(text, document_type)


def split_document_chunks(documents: List[Document], document_type: str = "default") -> List[Document]:
    """
    Split documents into chunks using the centralized service.
    
    Args:
        documents: List of Document objects to split
        document_type: Type of document for appropriate chunking strategy
        
    Returns:
        List of Document chunks
    """
    return text_splitter_service.split_documents(documents, document_type)
