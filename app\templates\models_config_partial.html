<!-- models_config_partial.html: Modular AI Models Configuration Section -->
<div class="config-section">
    <h2 class="config-section-header">LLM Models</h2>
    <p class="config-section-description">Select the language model used for generating responses to user queries.</p>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {% for model in models %}
            <div class="relative">
                <input type="radio" id="llm_{{ loop.index }}" name="llm_model" value="{{ model.name }}"
                       class="hidden peer" {% if model.name == selected_model %}checked{% endif %}>
                <label for="llm_{{ loop.index }}"
                       class="block p-4 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer
                              {% if model.name == selected_model %}border-blue-500 ring-2 ring-blue-500{% endif %}
                              peer-checked:border-blue-500 peer-checked:ring-2 peer-checked:ring-blue-500
                              hover:bg-gray-50 dark:hover:bg-gray-600 transition-all
                              {% if model.name == default_llm %}border-green-200 dark:border-green-600{% endif %}">
                    <div class="font-medium text-gray-900 dark:text-gray-100">{{ model.name }}</div>
                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Size: {{ model.size | filesizeformat }}
                    </div>
                    {% if model.name == default_llm %}
                    <div class="text-xs text-green-600 dark:text-green-400 mt-1 font-semibold">
                        Default Model
                    </div>
                    {% endif %}
                </label>
            </div>
        {% endfor %}
    </div>
</div>

<div class="config-section">
    <h2 class="config-section-header">Embedding Models</h2>
    <p class="config-section-description">Select the model used for generating vector embeddings of documents.</p>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {% for embedding in embeddings %}
            <div class="relative">
                <input type="radio" id="embed_{{ loop.index }}" name="embedding_model" value="{{ embedding.name }}"
                       class="hidden peer" {% if embedding.name == selected_embedding %}checked{% endif %}>
                <label for="embed_{{ loop.index }}"
                       class="block p-4 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer
                              {% if embedding.name == selected_embedding %}border-blue-500 ring-2 ring-blue-500{% endif %}
                              peer-checked:border-blue-500 peer-checked:ring-2 peer-checked:ring-blue-500
                              hover:bg-gray-50 dark:hover:bg-gray-600 transition-all
                              {% if embedding.name == default_embedding %}border-green-200 dark:border-green-600{% endif %}">
                    <div class="font-medium text-gray-900 dark:text-gray-100">{{ embedding.name }}</div>
                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Size: {{ embedding.size | filesizeformat }}
                    </div>
                    {% if embedding.name == default_embedding %}
                    <div class="text-xs text-green-600 dark:text-green-400 mt-1 font-semibold">
                        Default Model
                    </div>
                    {% endif %}
                </label>
            </div>
        {% endfor %}
    </div>
</div>

<div class="config-section">
    <h2 class="config-section-header">Vision Models</h2>
    <p class="config-section-description">Select the model used for analyzing images in documents.</p>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {% for vision in vision_models %}
            <div class="relative">
                <input type="radio" id="vision_{{ loop.index }}" name="vision_model" value="{{ vision.name }}"
                       class="hidden peer" {% if vision.name == selected_vision %}checked{% endif %}>
                <label for="vision_{{ loop.index }}"
                       class="block p-4 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer
                              {% if vision.name == selected_vision %}border-blue-500 ring-2 ring-blue-500{% endif %}
                              peer-checked:border-blue-500 peer-checked:ring-2 peer-checked:ring-blue-500
                              hover:bg-gray-50 dark:hover:bg-gray-600 transition-all
                              {% if vision.name == default_vision %}border-green-200 dark:border-green-600{% endif %}">
                    <div class="font-medium text-gray-900 dark:text-gray-100">{{ vision.name }}</div>
                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Size: {{ vision.size | filesizeformat }}
                    </div>
                    {% if vision.name == default_vision %}
                    <div class="text-xs text-green-600 dark:text-green-400 mt-1 font-semibold">
                        Default Model
                    </div>
                    {% endif %}
                </label>
            </div>
        {% endfor %}
    </div>
</div>

<div class="config-section">
    <h2 class="config-section-header">Vision Model</h2>
    <p class="config-section-description">Select the vision model used for analyzing images in chat responses. Both Llama 3.2 Vision and Gemma 3 multimodal models (4B-IT, 12B-IT) are supported.</p>
    <div class="flex flex-col space-y-4">
        <label class="inline-flex items-center">
            <input type="checkbox" name="use_vision" class="form-checkbox h-5 w-5 text-blue-600"
                   {% if use_vision %}checked{% endif %}>
            <span class="ml-2 text-gray-700 dark:text-gray-300">Enable Vision Model for Chat</span>
        </label>
        <div class="dependency-indicator">
            <span class="text-blue-600 dark:text-blue-400">↑</span> Controls whether images are analyzed during chat
        </div>
    </div>
</div>

<div class="config-section">
    <h2 class="config-section-header">Model Parameters</h2>
    <p class="config-section-description">Configure parameters for the LLM model to adjust its behavior.</p>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
        <!-- Temperature -->
        <div>
            <label for="temperature" class="block text-sm font-medium text-gray-700 mb-1">Temperature</label>
            <div class="flex items-center">
                <input type="range" id="temperature" name="temperature" min="0" max="1" step="0.05"
                       value="{{ temperature|default(0.7) }}" class="form-range mr-3">
                <span id="temperatureValue" class="badge bg-secondary">{{ temperature|default(0.7) }}</span>
            </div>
            <small class="text-muted">Controls randomness (0 = deterministic, 1 = creative)</small>
        </div>
        <!-- Top P -->
        <div>
            <label for="top_p" class="block text-sm font-medium text-gray-700 mb-1">Top P</label>
            <input type="number" id="top_p" name="top_p" min="0" max="1" step="0.01" value="{{ top_p|default(0.9) }}"
                   class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline">
            <small class="text-muted">Limits nucleus sampling to the smallest set whose cumulative probability exceeds this value</small>
        </div>
        <!-- Top K -->
        <div>
            <label for="top_k" class="block text-sm font-medium text-gray-700 mb-1">Top K</label>
            <input type="number" id="top_k" name="top_k" min="1" max="100" step="1" value="{{ top_k|default(40) }}"
                   class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline">
            <small class="text-muted">Limits sampling to the top K most likely tokens</small>
        </div>
        <!-- Repeat Penalty -->
        <div>
            <label for="repeat_penalty" class="block text-sm font-medium text-gray-700 mb-1">Repeat Penalty</label>
            <input type="number" id="repeat_penalty" name="repeat_penalty" min="1" max="2" step="0.01" value="{{ repeat_penalty|default(1.1) }}"
                   class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline">
            <small class="text-muted">Penalizes repeated tokens (higher = less repetition)</small>
        </div>
        <!-- Context Window -->
        <div>
            <label for="num_ctx" class="block text-sm font-medium text-gray-700 mb-1">Context Window (Tokens)</label>
            <input type="number" id="num_ctx" name="num_ctx" min="512" max="32768" step="1" value="{{ num_ctx|default(4096) }}"
                   class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline">
            <small class="text-muted">Maximum number of tokens the model can consider at once</small>
        </div>
        <!-- Max Tokens to Predict -->
        <div>
            <label for="num_predict" class="block text-sm font-medium text-gray-700 mb-1">Max Tokens to Predict</label>
            <input type="number" id="num_predict" name="num_predict" min="64" max="4096" step="1" value="{{ num_predict|default(256) }}"
                   class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline">
            <small class="text-muted">Maximum number of tokens the model will generate in a response</small>
        </div>
        <!-- System Prompt -->
        <div class="col-span-1 md:col-span-2">
            <label for="system_prompt" class="block text-sm font-medium text-gray-700 mb-1">System Prompt</label>
            <textarea id="system_prompt" name="system_prompt" rows="3"
                      class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline">{{ system_prompt|default('You are a helpful assistant for the ERDB (Ecosystems Research and Development Bureau). Answer questions based on the provided context.') }}</textarea>
            <small class="text-muted">Instructions for the model's behavior and persona</small>
        </div>
    </div>
</div>

<div class="flex justify-end mt-6">
    <button type="button" id="saveModelsBtn"
        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
        Save Models Settings
    </button>
</div> 