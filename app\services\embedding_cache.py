"""
Embedding Caching Service

This module provides two-tier caching (memory + disk) for embeddings to eliminate
redundant computations and significantly improve performance.
"""

import os
import hashlib
import pickle
import logging
import threading
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from app.services.cache_service import CacheService

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EmbeddingCache:
    """
    Two-tier embedding cache with memory and disk storage.
    
    This cache eliminates redundant embedding computations by storing results
    both in memory (for fast access) and on disk (for persistence).
    """
    
    def __init__(self, cache_dir: str = "./data/embedding_cache", memory_ttl: int = 3600):
        """
        Initialize the EmbeddingCache.
        
        Args:
            cache_dir: Directory for disk cache storage
            memory_ttl: Time-to-live for memory cache in seconds (default: 1 hour)
        """
        self.cache_dir = cache_dir
        self.memory_cache = CacheService(default_ttl=memory_ttl)
        self._disk_cache_lock = threading.Lock()
        
        # Create cache directory if it doesn't exist
        try:
            os.makedirs(cache_dir, exist_ok=True)
            logger.info(f"EmbeddingCache initialized with cache_dir: {cache_dir}")
        except Exception as e:
            logger.error(f"Failed to create cache directory {cache_dir}: {e}")
            raise
    
    def _get_cache_key(self, model_name: str, text: str) -> str:
        """
        Generate cache key for text and model combination.
        
        Args:
            model_name: Name of the embedding model
            text: Text content to embed
            
        Returns:
            SHA256 hash as cache key
        """
        # Normalize inputs to ensure consistent caching
        normalized_model = model_name.strip().lower()
        normalized_text = text.strip()
        
        # Create content string for hashing
        content = f"{normalized_model}:{normalized_text}"
        
        # Generate SHA256 hash
        cache_key = hashlib.sha256(content.encode('utf-8')).hexdigest()
        
        logger.debug(f"Generated cache key {cache_key[:16]}... for model {model_name}")
        return cache_key
    
    def _get_disk_cache_path(self, cache_key: str) -> str:
        """
        Get the disk cache file path for a cache key.
        
        Args:
            cache_key: Cache key hash
            
        Returns:
            Full path to cache file
        """
        return os.path.join(self.cache_dir, f"{cache_key}.pkl")
    
    def get_embedding(self, model_name: str, text: str) -> Optional[List[float]]:
        """
        Get cached embedding if available.
        
        Args:
            model_name: Name of the embedding model
            text: Text content to embed
            
        Returns:
            Cached embedding vector or None if not found
        """
        if not text or not text.strip():
            logger.warning("Empty text provided for embedding cache lookup")
            return None
        
        cache_key = self._get_cache_key(model_name, text)
        
        # Check memory cache first (fastest)
        embedding = self.memory_cache.get(cache_key)
        if embedding is not None:
            logger.debug(f"Cache HIT (memory) for key {cache_key[:16]}...")
            return embedding
        
        # Check disk cache (slower but persistent)
        embedding = self._get_from_disk_cache(cache_key)
        if embedding is not None:
            # Store in memory cache for faster future access
            self.memory_cache.set(cache_key, embedding)
            logger.debug(f"Cache HIT (disk) for key {cache_key[:16]}...")
            return embedding
        
        logger.debug(f"Cache MISS for key {cache_key[:16]}...")
        return None
    
    def _get_from_disk_cache(self, cache_key: str) -> Optional[List[float]]:
        """
        Get embedding from disk cache.
        
        Args:
            cache_key: Cache key hash
            
        Returns:
            Cached embedding vector or None if not found/corrupted
        """
        cache_file = self._get_disk_cache_path(cache_key)
        
        if not os.path.exists(cache_file):
            return None
        
        try:
            with self._disk_cache_lock:
                with open(cache_file, 'rb') as f:
                    cache_data = pickle.load(f)
                
                # Validate cache data structure
                if not isinstance(cache_data, dict) or 'embedding' not in cache_data:
                    logger.warning(f"Invalid cache data structure in {cache_file}")
                    self._remove_corrupted_cache_file(cache_file)
                    return None
                
                # Check if cache has expired (optional disk TTL)
                if 'timestamp' in cache_data:
                    cache_age = datetime.now() - cache_data['timestamp']
                    # Disk cache TTL: 24 hours
                    if cache_age > timedelta(hours=24):
                        logger.debug(f"Disk cache expired for {cache_file}")
                        self._remove_corrupted_cache_file(cache_file)
                        return None
                
                return cache_data['embedding']
                
        except Exception as e:
            logger.warning(f"Failed to read disk cache {cache_file}: {e}")
            self._remove_corrupted_cache_file(cache_file)
            return None
    
    def set_embedding(self, model_name: str, text: str, embedding: List[float]) -> None:
        """
        Cache embedding both in memory and on disk.
        
        Args:
            model_name: Name of the embedding model
            text: Text content that was embedded
            embedding: Embedding vector to cache
        """
        if not text or not text.strip() or not embedding:
            logger.warning("Invalid parameters provided for embedding caching")
            return
        
        cache_key = self._get_cache_key(model_name, text)
        
        # Store in memory cache (immediate access)
        self.memory_cache.set(cache_key, embedding)
        
        # Store in disk cache (persistent storage)
        self._set_to_disk_cache(cache_key, embedding, model_name)
        
        logger.debug(f"Cached embedding for key {cache_key[:16]}... (memory + disk)")
    
    def _set_to_disk_cache(self, cache_key: str, embedding: List[float], model_name: str) -> None:
        """
        Store embedding in disk cache.
        
        Args:
            cache_key: Cache key hash
            embedding: Embedding vector to cache
            model_name: Name of the embedding model (for metadata)
        """
        cache_file = self._get_disk_cache_path(cache_key)
        
        try:
            cache_data = {
                'embedding': embedding,
                'model_name': model_name,
                'timestamp': datetime.now(),
                'cache_version': '1.0'
            }
            
            with self._disk_cache_lock:
                with open(cache_file, 'wb') as f:
                    pickle.dump(cache_data, f)
                    
        except Exception as e:
            logger.warning(f"Failed to cache embedding to disk {cache_file}: {e}")
    
    def _remove_corrupted_cache_file(self, cache_file: str) -> None:
        """
        Remove corrupted cache file.
        
        Args:
            cache_file: Path to corrupted cache file
        """
        try:
            if os.path.exists(cache_file):
                os.remove(cache_file)
                logger.debug(f"Removed corrupted cache file: {cache_file}")
        except Exception as e:
            logger.warning(f"Failed to remove corrupted cache file {cache_file}: {e}")
    
    def clear_cache(self, memory_only: bool = False) -> None:
        """
        Clear embedding cache.
        
        Args:
            memory_only: If True, only clear memory cache. If False, clear both.
        """
        # Clear memory cache
        self.memory_cache.clear()
        logger.info("Memory cache cleared")
        
        if not memory_only:
            # Clear disk cache
            try:
                with self._disk_cache_lock:
                    for filename in os.listdir(self.cache_dir):
                        if filename.endswith('.pkl'):
                            file_path = os.path.join(self.cache_dir, filename)
                            os.remove(file_path)
                logger.info("Disk cache cleared")
            except Exception as e:
                logger.error(f"Failed to clear disk cache: {e}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics.
        
        Returns:
            Dictionary with cache statistics
        """
        try:
            # Count disk cache files
            disk_files = 0
            disk_size = 0
            if os.path.exists(self.cache_dir):
                for filename in os.listdir(self.cache_dir):
                    if filename.endswith('.pkl'):
                        disk_files += 1
                        file_path = os.path.join(self.cache_dir, filename)
                        disk_size += os.path.getsize(file_path)
            
            return {
                'memory_cache_size': len(self.memory_cache._cache),
                'disk_cache_files': disk_files,
                'disk_cache_size_mb': disk_size / (1024 * 1024),
                'cache_directory': self.cache_dir
            }
        except Exception as e:
            logger.error(f"Failed to get cache stats: {e}")
            return {'error': str(e)}
    
    def cleanup_expired_cache(self) -> int:
        """
        Clean up expired disk cache files.
        
        Returns:
            Number of files cleaned up
        """
        cleaned_files = 0
        
        try:
            with self._disk_cache_lock:
                for filename in os.listdir(self.cache_dir):
                    if not filename.endswith('.pkl'):
                        continue
                    
                    file_path = os.path.join(self.cache_dir, filename)
                    try:
                        with open(file_path, 'rb') as f:
                            cache_data = pickle.load(f)
                        
                        if 'timestamp' in cache_data:
                            cache_age = datetime.now() - cache_data['timestamp']
                            if cache_age > timedelta(hours=24):
                                os.remove(file_path)
                                cleaned_files += 1
                                
                    except Exception:
                        # Remove corrupted files
                        os.remove(file_path)
                        cleaned_files += 1
            
            if cleaned_files > 0:
                logger.info(f"Cleaned up {cleaned_files} expired/corrupted cache files")
                
        except Exception as e:
            logger.error(f"Failed to cleanup expired cache: {e}")
        
        return cleaned_files


# Global instance for use throughout the application
embedding_cache = EmbeddingCache()


def get_embedding_cache() -> EmbeddingCache:
    """
    Get the global EmbeddingCache instance.
    
    Returns:
        Global EmbeddingCache instance
    """
    return embedding_cache


# Convenience functions for direct use
def get_cached_embedding(model_name: str, text: str) -> Optional[List[float]]:
    """
    Get cached embedding if available.
    
    Args:
        model_name: Name of the embedding model
        text: Text content to embed
        
    Returns:
        Cached embedding vector or None if not found
    """
    return embedding_cache.get_embedding(model_name, text)


def cache_embedding(model_name: str, text: str, embedding: List[float]) -> None:
    """
    Cache embedding for future use.
    
    Args:
        model_name: Name of the embedding model
        text: Text content that was embedded
        embedding: Embedding vector to cache
    """
    embedding_cache.set_embedding(model_name, text, embedding)
