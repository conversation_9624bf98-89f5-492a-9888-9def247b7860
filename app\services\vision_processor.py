import os
import base64
import logging
import requests
from io import BytesIO
from PIL import Image
import json
from typing import List, Dict, Any
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Constants
# Vision model can be Llama 3.2 Vision or Gemma 3 multimodal models (4B, 12B variants)
# Supported models:
# - llama3.2-vision:11b-instruct-q4_K_M (default)
# - gemma3:4b-it-q4_K_M
# - gemma3:12b-it-q4_K_M
VISION_MODEL = os.getenv('VISION_MODEL', 'llama3.2-vision:11b-instruct-q4_K_M')
OLLAMA_API_HOST = os.getenv('OLLAMA_API_HOST', 'http://localhost:11434')
MAX_IMAGE_SIZE = (1024, 1024)  # Maximum image dimensions for processing
TEMP_FOLDER = os.getenv("TEMP_FOLDER", "./data/temp")  # Base temp folder for all files
TEMP_DIR = os.path.join(TEMP_FOLDER, "vision_cache")  # Vision cache directory
CACHE_ENABLED = os.getenv("VISION_CACHE_ENABLED", "true").lower() == "true"

# Ensure temp directories exist
os.makedirs(TEMP_DIR, exist_ok=True)

def encode_image_to_base64(image_path: str) -> str:
    """
    Encode an image file to base64 string.

    Args:
        image_path: Path to the image file

    Returns:
        Base64 encoded string of the image
    """
    try:
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    except Exception as e:
        logger.error(f"Error encoding image {image_path}: {str(e)}")
        return None

def encode_image_url_to_base64(image_path_or_url: str) -> str:
    """
    Process an image from URL or local file and encode it to base64 string.
    For local files (starting with 'file://' or containing 'data/temp/'),
    it will use the file directly instead of downloading it again.

    Args:
        image_path_or_url: URL of the image, local file path with file:// prefix, or direct file path

    Returns:
        Base64 encoded string of the image
    """
    try:
        # First check if this is a direct file path that exists
        if os.path.exists(image_path_or_url) and os.path.isfile(image_path_or_url):
            logger.info(f"Using direct file path: {image_path_or_url}")
            img = Image.open(image_path_or_url)
            img.thumbnail(MAX_IMAGE_SIZE, Image.LANCZOS)

            # Convert to base64
            buffered = BytesIO()
            img.save(buffered, format="JPEG")
            return base64.b64encode(buffered.getvalue()).decode('utf-8')

        # Check if this is a local file path with file:// prefix or containing data/temp/
        is_local_file = image_path_or_url.startswith('file://') or ('/data/temp/' in image_path_or_url) or ('\\_temp\\' in image_path_or_url)

        if is_local_file:
            # Extract the local file path
            if image_path_or_url.startswith('file://'):
                local_path = image_path_or_url[7:]  # Remove 'file://' prefix
            else:
                # For server URLs like http://localhost:8080/CATEGORY/PDF_NAME/pdf_images/image.jpg
                # Extract the path after the server part
                parts = image_path_or_url.split('/data/temp/')
                if len(parts) > 1:
                    local_path = os.path.join(TEMP_FOLDER, parts[1])
                else:
                    parts = image_path_or_url.split('\\_temp\\')
                    if len(parts) > 1:
                        local_path = os.path.join(TEMP_FOLDER, parts[1])
                    else:
                        # If we can't extract a local path, treat it as a regular URL
                        local_path = None
                        is_local_file = False

            # If we have a valid local path, use it directly
            if is_local_file and local_path and os.path.exists(local_path):
                logger.info(f"Using existing local file: {local_path}")
                # Resize the image if needed
                img = Image.open(local_path)
                img.thumbnail(MAX_IMAGE_SIZE, Image.LANCZOS)

                # Convert to base64
                buffered = BytesIO()
                img.save(buffered, format="JPEG")
                return base64.b64encode(buffered.getvalue()).decode('utf-8')

        # For non-local files or if local file doesn't exist, proceed with normal URL handling
        # Check if we have a cached version
        if CACHE_ENABLED:
            # Create a filename based on the URL (hashed)
            import hashlib
            filename = hashlib.md5(image_path_or_url.encode()).hexdigest() + ".jpg"
            cache_path = os.path.join(TEMP_DIR, filename)

            # If cached version exists, use it
            if os.path.exists(cache_path):
                logger.info(f"Using cached image for {image_path_or_url}")
                return encode_image_to_base64(cache_path)

        # Download the image
        response = requests.get(image_path_or_url, timeout=10, verify=False)
        if response.status_code != 200:
            logger.error(f"Failed to download image from {image_path_or_url}: HTTP {response.status_code}")
            return None

        # Open and resize the image
        img = Image.open(BytesIO(response.content))
        img.thumbnail(MAX_IMAGE_SIZE, Image.LANCZOS)

        # Save to temp file if caching is enabled
        if CACHE_ENABLED:
            img.save(cache_path)
            return encode_image_to_base64(cache_path)

        # Convert to base64
        buffered = BytesIO()
        img.save(buffered, format="JPEG")
        return base64.b64encode(buffered.getvalue()).decode('utf-8')
    except Exception as e:
        logger.error(f"Error processing image URL {image_path_or_url}: {str(e)}")
        return None

def generate_contextual_caption(image_path_or_url: str, document_context: str = None, query: str = None, is_pdf_image: bool = False) -> Dict[str, Any]:
    """
    Generate a contextually relevant caption for an image using the vision model.

    Args:
        image_path_or_url: Path or URL of the image to analyze
        document_context: Text context from the document containing the image
        query: The user's query that led to retrieving this image
        is_pdf_image: Whether the image is from a PDF document

    Returns:
        Dictionary with caption and analysis results
    """
    try:
        # Encode the image
        base64_image = encode_image_url_to_base64(image_path_or_url)
        if not base64_image:
            return {"error": f"Failed to encode image from {image_path_or_url}"}

        # Create a contextually relevant prompt
        if document_context and len(document_context) > 0:
            # Truncate context if it's too long (max 1000 chars)
            context_excerpt = document_context[:1000] + "..." if len(document_context) > 1000 else document_context

            # Build a prompt that incorporates the document context
            if query:
                prompt = f"""
                This image appears in a document about the following topic:

                DOCUMENT CONTEXT:
                {context_excerpt}

                USER QUERY:
                {query}

                Generate a contextually relevant caption (1-2 sentences) that:
                1. Explains how this image relates to the document's topic and the user's query
                2. Uses relevant terminology from the document domain
                3. Provides meaningful context rather than just describing visual elements
                4. Is concise but informative

                Your caption should help the user understand why this image is relevant to their query.
                """
            else:
                prompt = f"""
                This image appears in a document about the following topic:

                DOCUMENT CONTEXT:
                {context_excerpt}

                Generate a contextually relevant caption (1-2 sentences) that:
                1. Explains how this image relates to the document's topic
                2. Uses relevant terminology from the document domain
                3. Provides meaningful context rather than just describing visual elements
                4. Is concise but informative
                """
        elif query:
            # If we only have the query but no document context
            prompt = f"""
            USER QUERY:
            {query}

            Generate a contextually relevant caption (1-2 sentences) that:
            1. Explains how this image might relate to the user's query
            2. Is concise but informative
            3. Provides meaningful context rather than just describing visual elements
            """
        else:
            # Fallback to basic analysis with improved instructions
            prompt = """
            Generate a concise, informative caption (1-2 sentences) for this image that:
            1. Focuses on the main subject and its significance
            2. Avoids generic descriptions that merely list objects
            3. Highlights any text, data, or key information visible in the image
            4. Is suitable for educational or research contexts
            """

        # Prepare the request for Ollama
        api_url = f"{OLLAMA_API_HOST}/api/generate"

        # Create the message with image
        payload = {
            "model": VISION_MODEL,
            "prompt": prompt,
            "images": [base64_image],
            "stream": False
        }

        # Send the request
        start_time = time.time()
        response = requests.post(api_url, json=payload)
        processing_time = time.time() - start_time

        if response.status_code != 200:
            logger.error(f"Error from Ollama API: {response.status_code} - {response.text}")
            return {"error": f"API error: {response.status_code}", "image_url": image_path_or_url}

        # Parse the response
        result = response.json()
        caption = result.get("response", "").strip()

        # Clean up the caption - remove quotes if present
        caption = caption.strip('"\'')

        # Truncate if too long (max 150 chars)
        if len(caption) > 150:
            caption = caption[:147] + "..."

        return {
            "contextual_caption": caption,
            "description": caption,  # For backward compatibility
            "image_url": image_path_or_url,
            "processing_time": processing_time,
            "model": VISION_MODEL,
            "has_context": bool(document_context or query)
        }
    except Exception as e:
        logger.error(f"Error generating contextual caption for {image_path_or_url}: {str(e)}")
        return {"error": str(e), "image_url": image_path_or_url}

def analyze_image(image_path_or_url: str, prompt: str = "Describe this image in detail.", document_context: str = None, query: str = None) -> Dict[str, Any]:
    """
    Analyze an image using the selected vision model (Llama 3.2 Vision or Gemma 3).

    Args:
        image_path_or_url: Path or URL of the image to analyze
        prompt: Prompt to send to the vision model
        document_context: Optional text context from the document containing the image
        query: Optional user query related to the image

    Returns:
        Dictionary with analysis results
    """
    try:
        # If we have context, use the contextual caption generator instead
        if (document_context or query) and "Generate a contextually relevant caption" not in prompt:
            return generate_contextual_caption(image_path_or_url, document_context, query)

        # Otherwise, proceed with standard analysis
        base64_image = encode_image_url_to_base64(image_path_or_url)
        if not base64_image:
            return {"error": f"Failed to encode image from {image_path_or_url}"}

        # Prepare the request for Ollama
        api_url = f"{OLLAMA_API_HOST}/api/generate"

        # Create the message with image
        payload = {
            "model": VISION_MODEL,
            "prompt": prompt,
            "images": [base64_image],
            "stream": False
        }

        # Send the request
        start_time = time.time()
        response = requests.post(api_url, json=payload)
        processing_time = time.time() - start_time

        if response.status_code != 200:
            logger.error(f"Error from Ollama API: {response.status_code} - {response.text}")
            return {"error": f"API error: {response.status_code}", "image_url": image_path_or_url}

        # Parse the response
        result = response.json()

        # Return the analysis results
        return {
            "description": result.get("response", ""),
            "image_url": image_path_or_url,
            "processing_time": processing_time,
            "model": VISION_MODEL
        }
    except Exception as e:
        logger.error(f"Error analyzing image {image_path_or_url}: {str(e)}")
        return {"error": str(e), "image_url": image_path_or_url}

def detect_image_content(image_path_or_url: str, is_pdf_image: bool = False, document_context: str = None, query: str = None) -> Dict[str, Any]:
    """
    Detect the content of an image (logos, text, objects, etc.) and generate a contextual caption if context is provided.

    Args:
        image_path_or_url: Path or URL of the image to analyze (can be a direct file path or URL)
        is_pdf_image: Whether the image is from a PDF document
        document_context: Optional text context from the document containing the image
        query: Optional user query related to the image

    Returns:
        Dictionary with detection results and contextual caption
    """
    # Check the appropriate vision model setting based on context
    if is_pdf_image:
        # For PDF images during embedding, check USE_VISION_MODEL_DURING_EMBEDDING
        use_vision = os.getenv('USE_VISION_MODEL_DURING_EMBEDDING', 'true').lower() == 'true'
        if not use_vision:
            logger.info(f"Vision model is disabled for PDF embedding. Skipping analysis for {image_path_or_url}")
            return {
                "error": "Vision model is disabled for PDF embedding",
                "image_url": image_path_or_url,
                "vision_disabled": True
            }
    else:
        # For regular image analysis during chat, check USE_VISION_MODEL
        use_vision = os.getenv('USE_VISION_MODEL', 'true').lower() == 'true'
        if not use_vision:
            logger.info(f"Vision model is disabled for chat. Skipping analysis for {image_path_or_url}")
            return {
                "error": "Vision model is disabled for chat",
                "image_url": image_path_or_url,
                "vision_disabled": True
            }

    # If we have document context or query, generate a contextual caption first
    if document_context or query:
        contextual_result = generate_contextual_caption(image_path_or_url, document_context, query, is_pdf_image)

        # If there was an error with the contextual caption, fall back to standard analysis
        if "error" in contextual_result:
            logger.warning(f"Error generating contextual caption, falling back to standard analysis: {contextual_result.get('error')}")
        else:
            # For PDF images, we still need the full analysis for filtering and metadata
            if is_pdf_image:
                # Use a more detailed prompt for PDF images
                prompt = """
                Analyze this image extracted from a PDF document and provide the following information in JSON format:

                1. Is this a logo or brand image? (true/false)
                2. What objects or elements are visible in the image?
                3. Is there any text visible in the image? If so, what does it say?
                4. What category best describes this image? Choose from: photograph, diagram, chart, graph, illustration, logo, icon, decoration, table, screenshot, map, or other.
                5. Is this image purely decorative or does it contain meaningful information? (decorative/informative)
                6. On a scale of 1-10, how relevant is this image likely to be to the document's main content? (1=not relevant, 10=highly relevant)
                7. Provide a detailed description of the image content (2-3 sentences).

                Format your response as valid JSON with these keys:
                {
                    "is_logo": boolean,
                    "objects": [list of strings],
                    "text": string or null,
                    "category": string,
                    "is_decorative": boolean,
                    "relevance_score": number,
                    "description": string
                }
                """

                # Get the standard analysis
                standard_result = analyze_image(image_path_or_url, prompt)

                if "error" not in standard_result:
                    # Try to parse the JSON response
                    try:
                        # Extract JSON from the response text
                        response_text = standard_result["description"]

                        # Find JSON content (it might be embedded in other text)
                        import re
                        json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
                        if json_match:
                            json_str = json_match.group(0)
                            content_data = json.loads(json_str)

                            # Add the contextual caption to the standard analysis
                            content_data["contextual_caption"] = contextual_result.get("contextual_caption")
                            content_data["description"] = contextual_result.get("contextual_caption")  # Replace with contextual caption
                            content_data["raw_response"] = response_text
                            content_data["image_url"] = image_path_or_url
                            content_data["has_context"] = True

                            return content_data
                    except Exception as e:
                        logger.error(f"Error parsing vision model response: {str(e)}")

            # For non-PDF images or if parsing failed, just return the contextual result
            return contextual_result

    # If no context is provided or contextual captioning failed, use standard analysis
    # Use a more detailed prompt for PDF images
    if is_pdf_image:
        prompt = """
        Analyze this image extracted from a PDF document and provide the following information in JSON format:

        1. Is this a logo or brand image? (true/false)
        2. What objects or elements are visible in the image?
        3. Is there any text visible in the image? If so, what does it say?
        4. What category best describes this image? Choose from: photograph, diagram, chart, graph, illustration, logo, icon, decoration, table, screenshot, map, or other.
        5. Is this image purely decorative or does it contain meaningful information? (decorative/informative)
        6. On a scale of 1-10, how relevant is this image likely to be to the document's main content? (1=not relevant, 10=highly relevant)
        7. Provide a detailed description of the image content (2-3 sentences).

        Format your response as valid JSON with these keys:
        {
            "is_logo": boolean,
            "objects": [list of strings],
            "text": string or null,
            "category": string,
            "is_decorative": boolean,
            "relevance_score": number,
            "description": string
        }
        """
    else:
        prompt = """
        Analyze this image and provide the following information in JSON format:
        1. Is this a logo or brand image? (true/false)
        2. What objects or elements are visible in the image?
        3. Is there any text visible in the image? If so, what does it say?
        4. What category best describes this image? (e.g., photograph, diagram, chart, illustration, logo, etc.)
        5. Brief description (1-2 sentences)

        Format your response as valid JSON with these keys:
        {"is_logo": boolean, "objects": [list of strings], "text": string or null, "category": string, "description": string}
        """

    result = analyze_image(image_path_or_url, prompt)

    if "error" in result:
        return result

    # Try to parse the JSON response
    try:
        # Extract JSON from the response text
        response_text = result["description"]

        # Find JSON content (it might be embedded in other text)
        import re
        json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
        if json_match:
            json_str = json_match.group(0)
            content_data = json.loads(json_str)

            # Add the raw response and image URL
            content_data["raw_response"] = response_text
            content_data["image_url"] = image_path_or_url

            return content_data
        else:
            # If no JSON found, return a structured response based on the text
            return {
                "is_logo": "logo" in response_text.lower(),
                "objects": [],
                "text": None,
                "category": "unknown",
                "description": response_text[:200],
                "raw_response": response_text,
                "image_url": image_path_or_url
            }
    except Exception as e:
        logger.error(f"Error parsing vision model response: {str(e)}")
        return {
            "error": f"Failed to parse response: {str(e)}",
            "raw_response": result.get("description", ""),
            "image_url": image_path_or_url
        }

def calculate_image_relevance(content: Dict[str, Any]) -> int:
    """
    Calculate a relevance score for an image based on its content analysis.

    Args:
        content: Dictionary with image content analysis

    Returns:
        Relevance score from 1-10 (higher is more relevant)
    """
    # If the vision model already provided a relevance score, use it
    if "relevance_score" in content and isinstance(content["relevance_score"], (int, float)):
        return min(max(int(content["relevance_score"]), 1), 10)

    # Start with a base score
    score = 5

    # Adjust score based on content attributes

    # Logos and decorative images are less relevant
    if content.get("is_logo", False):
        score -= 3

    if content.get("is_decorative", False):
        score -= 3

    # Images with text are often more relevant
    if content.get("text") and len(content.get("text", "")) > 0:
        score += 1

    # Adjust based on category
    category = content.get("category", "").lower()
    if category in ["chart", "graph", "diagram", "table", "map"]:
        # Informational graphics are highly relevant
        score += 3
    elif category in ["photograph", "screenshot"]:
        # Photographs and screenshots are moderately relevant
        score += 1
    elif category in ["logo", "icon", "decoration"]:
        # Decorative elements are less relevant
        score -= 2

    # Ensure score is within 1-10 range
    return min(max(score, 1), 10)

def should_filter_image(image_path_or_url: str, filter_sensitivity: str = "medium", is_pdf_image: bool = False) -> bool:
    """
    Determine if an image should be filtered out based on content analysis.

    Args:
        image_path_or_url: Path or URL of the image to analyze
        filter_sensitivity: Sensitivity level for filtering (low, medium, high)
        is_pdf_image: Whether the image is from a PDF document being embedded

    Returns:
        True if the image should be filtered out, False otherwise
    """
    # Check the appropriate vision model setting based on context
    if is_pdf_image:
        # For PDF images during embedding, check USE_VISION_MODEL_DURING_EMBEDDING
        use_vision = os.getenv('USE_VISION_MODEL_DURING_EMBEDDING', 'true').lower() == 'true'
    else:
        # For regular image analysis during chat, check USE_VISION_MODEL
        use_vision = os.getenv('USE_VISION_MODEL', 'true').lower() == 'true'

    if not use_vision:
        # If vision model is disabled, only use basic filename-based filtering
        if 'logo' in image_path_or_url.lower() and image_path_or_url.lower().endswith('.png'):
            return True
        return False

    # Set threshold based on sensitivity
    if filter_sensitivity == "low":
        threshold = 3  # More permissive
    elif filter_sensitivity == "high":
        threshold = 7  # More strict
    else:  # medium (default)
        threshold = 5

    # Skip filtering for non-PNG images
    if not image_path_or_url.lower().endswith('.png'):
        return False

    # First check if it's a logo based on filename (existing logic)
    if 'logo' in image_path_or_url.lower():
        return True

    # For PNG files without 'logo' in the name, use vision model to check
    try:
        content = detect_image_content(image_path_or_url)

        # If vision is disabled (checked in detect_image_content), don't filter
        if content.get("vision_disabled", False):
            return False

        # Filter out if the vision model identifies it as a logo
        if content.get("is_logo", False):
            logger.info(f"Vision model identified logo in {image_path_or_url}")
            return True

        # Calculate relevance score
        relevance = calculate_image_relevance(content)

        # Filter based on relevance threshold
        if relevance < threshold:
            logger.info(f"Filtering image {image_path_or_url} with relevance score {relevance} (threshold: {threshold})")
            return True

        return False
    except Exception as e:
        logger.error(f"Error in content-based filtering for {image_path_or_url}: {str(e)}")
        # If analysis fails, fall back to the filename-based approach
        return False

def batch_analyze_images(image_paths_or_urls: List[str], max_images: int = 5, is_pdf_images: bool = False) -> List[Dict[str, Any]]:
    """
    Analyze multiple images in batch.

    Args:
        image_paths_or_urls: List of image paths or URLs to analyze
        max_images: Maximum number of images to analyze
        is_pdf_images: Whether the images are from PDF documents

    Returns:
        List of dictionaries with analysis results
    """
    # Check the appropriate vision model setting based on context
    if is_pdf_images:
        # For PDF images during embedding, check USE_VISION_MODEL_DURING_EMBEDDING
        use_vision = os.getenv('USE_VISION_MODEL_DURING_EMBEDDING', 'true').lower() == 'true'
        if not use_vision:
            logger.info("Vision model is disabled for PDF embedding. Skipping batch image analysis.")
            # Return basic info without analysis
            return [{"error": "Vision model is disabled for PDF embedding", "vision_disabled": True} for _ in image_paths_or_urls[:max_images]]
    else:
        # For regular image analysis during chat, check USE_VISION_MODEL
        use_vision = os.getenv('USE_VISION_MODEL', 'true').lower() == 'true'
        if not use_vision:
            logger.info("Vision model is disabled for chat. Skipping batch image analysis.")
            # Return basic info without analysis
            return [{"error": "Vision model is disabled for chat", "vision_disabled": True} for _ in image_paths_or_urls[:max_images]]

    results = []

    # Limit the number of images to analyze
    paths_to_process = image_paths_or_urls[:max_images]

    for path_or_url in paths_to_process:
        result = detect_image_content(path_or_url, is_pdf_image=is_pdf_images)
        results.append(result)

    return results

def analyze_pdf_images(pdf_images: List[Dict[str, Any]], max_images: int = 10, document_context: str = None, query: str = None) -> List[Dict[str, Any]]:
    """
    Analyze images extracted from PDF documents with contextual captioning.

    Args:
        pdf_images: List of image info dictionaries from PDF extraction
        max_images: Maximum number of images to analyze
        document_context: Optional text context from the document containing the images
        query: Optional user query related to the images

    Returns:
        List of enhanced image info dictionaries with analysis results and contextual captions
    """
    # Determine if this is for embedding or chat based on query presence
    is_for_embedding = query is None

    if is_for_embedding:
        # For PDF embedding, check USE_VISION_MODEL_DURING_EMBEDDING
        use_vision = os.getenv('USE_VISION_MODEL_DURING_EMBEDDING', 'true').lower() == 'true'
        if not use_vision:
            logger.info("Vision model is disabled for PDF embedding. Skipping PDF image analysis.")
            # Return the original images without analysis
            if pdf_images:
                pdf_images[0]["total_images"] = len(pdf_images)
                pdf_images[0]["analyzed_images"] = 0
                pdf_images[0]["vision_disabled"] = True
            return pdf_images
    else:
        # For chat queries, check USE_VISION_MODEL
        use_vision = os.getenv('USE_VISION_MODEL', 'true').lower() == 'true'
        if not use_vision:
            logger.info("Vision model is disabled for chat. Skipping PDF image analysis.")
            # Return the original images without analysis
            if pdf_images:
                pdf_images[0]["total_images"] = len(pdf_images)
                pdf_images[0]["analyzed_images"] = 0
                pdf_images[0]["vision_disabled"] = True
            return pdf_images

    # Make a copy to avoid modifying the original
    enhanced_images = []

    # Track how many images we've processed
    processed_count = 0

    # Extract document text from each image's source if available and no context was provided
    if not document_context:
        # Try to build context from image metadata
        doc_texts = []
        for img in pdf_images:
            if img.get("source_text"):
                doc_texts.append(img.get("source_text"))

        # Combine the texts if we found any
        if doc_texts:
            document_context = "\n\n".join(doc_texts)
            logger.info(f"Built document context from {len(doc_texts)} image source texts")

    # Process images
    for img in pdf_images:
        if processed_count >= max_images:
            # Add the remaining images without analysis
            enhanced_images.append(img.copy())
            continue

        # Make a copy of the image info
        enhanced_img = img.copy()

        # Skip if no URL
        if not enhanced_img.get("url"):
            enhanced_images.append(enhanced_img)
            continue

        # Get image-specific context if available
        img_context = document_context
        if not img_context and enhanced_img.get("source_text"):
            img_context = enhanced_img.get("source_text")

        try:
            # Create a full URL for the vision processor
            # Use environment variable for the server URL or default to localhost
            server_url = os.getenv('SERVER_URL', 'http://localhost:8080')
            img_url = f"{server_url}{enhanced_img['url']}"

            # Analyze the image with context
            analysis = detect_image_content(img_url, is_pdf_image=True, document_context=img_context, query=query)

            # Check if vision is disabled (from detect_image_content)
            if analysis.get("vision_disabled", False):
                enhanced_img["vision_disabled"] = True
                enhanced_img["analyzed"] = False
                enhanced_images.append(enhanced_img)
                continue

            # Store analysis results
            if "error" not in analysis:
                # Add contextual caption if available
                if analysis.get("contextual_caption"):
                    enhanced_img["contextual_caption"] = analysis.get("contextual_caption")
                    enhanced_img["ai_description"] = analysis.get("contextual_caption")
                    enhanced_img["description"] = analysis.get("contextual_caption")
                    enhanced_img["has_context"] = True
                # Fall back to standard description if no contextual caption
                elif analysis.get("description"):
                    enhanced_img["ai_description"] = analysis.get("description")
                    enhanced_img["description"] = analysis.get("description")

                # Add content metadata
                enhanced_img["is_logo"] = analysis.get("is_logo", False)
                enhanced_img["category"] = analysis.get("category", "unknown")
                enhanced_img["objects"] = analysis.get("objects", [])
                enhanced_img["has_text"] = analysis.get("text") is not None and len(analysis.get("text", "")) > 0
                enhanced_img["text_content"] = analysis.get("text")

                # Add new fields from the enhanced PDF image analysis
                if "is_decorative" in analysis:
                    enhanced_img["is_decorative"] = analysis.get("is_decorative", False)
                if "relevance_score" in analysis:
                    enhanced_img["relevance_score"] = analysis.get("relevance_score", 5)

                # Mark as analyzed
                enhanced_img["analyzed"] = True

                # Increment processed count
                processed_count += 1
            else:
                # Store error information
                enhanced_img["vision_error"] = analysis.get("error")
                enhanced_img["analyzed"] = False
        except Exception as e:
            logger.error(f"Error analyzing PDF image: {str(e)}")
            enhanced_img["vision_error"] = str(e)
            enhanced_img["analyzed"] = False

        enhanced_images.append(enhanced_img)

    # Add statistics
    if enhanced_images:
        analyzed_count = sum(1 for img in enhanced_images if img.get("analyzed", False))
        enhanced_images[0]["total_images"] = len(enhanced_images)
        enhanced_images[0]["analyzed_images"] = analyzed_count
        enhanced_images[0]["has_context"] = bool(document_context or query)

    return enhanced_images
