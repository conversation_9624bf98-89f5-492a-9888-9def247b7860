"""
Optimized Vector Database Service

This module provides optimized vector database initialization with caching
for embedding models and database instances to eliminate redundant operations.
"""

import os
import json
import logging
import threading
from typing import Dict, Optional, List
from langchain_chroma import Chroma
from langchain_ollama.embeddings import OllamaEmbeddings
from chromadb.config import Settings
import requests
from app.services.embedding_cache import get_embedding_cache

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration constants
CHROMA_PATH = os.getenv("UNIFIED_CHROMA_PATH", "./data/unified_chroma")
TEXT_EMBEDDING_MODEL = os.getenv("TEXT_EMBEDDING_MODEL", "mxbai-embed-large:latest")
OLLAMA_BASE_URL = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")

# Fallback embedding models in order of preference
FALLBACK_EMBEDDING_MODELS = ["mxbai-embed-large:latest", "bge-m3:latest", "nomic-embed-text:latest"]


class CachedOllamaEmbeddings:
    """
    Wrapper for OllamaEmbeddings that adds caching functionality.
    """

    def __init__(self, model: str, base_url: str = OLLAMA_BASE_URL):
        """
        Initialize cached embeddings wrapper.

        Args:
            model: Name of the embedding model
            base_url: Base URL for Ollama service
        """
        self.model = model
        self.base_url = base_url
        self._ollama_embeddings = OllamaEmbeddings(model=model, base_url=base_url)
        self._cache = get_embedding_cache()

        logger.debug(f"Initialized CachedOllamaEmbeddings for model: {model}")

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        Embed documents with caching.

        Args:
            texts: List of texts to embed

        Returns:
            List of embedding vectors
        """
        embeddings = []
        uncached_texts = []
        uncached_indices = []

        # Check cache for each text
        for i, text in enumerate(texts):
            cached_embedding = self._cache.get_embedding(self.model, text)
            if cached_embedding is not None:
                embeddings.append(cached_embedding)
            else:
                embeddings.append(None)  # Placeholder
                uncached_texts.append(text)
                uncached_indices.append(i)

        # Generate embeddings for uncached texts
        if uncached_texts:
            logger.debug(f"Generating {len(uncached_texts)} new embeddings (cache miss)")
            new_embeddings = self._ollama_embeddings.embed_documents(uncached_texts)

            # Cache new embeddings and fill placeholders
            for idx, embedding in zip(uncached_indices, new_embeddings):
                self._cache.set_embedding(self.model, texts[idx], embedding)
                embeddings[idx] = embedding
        else:
            logger.debug(f"All {len(texts)} embeddings retrieved from cache")

        return embeddings

    def embed_query(self, text: str) -> List[float]:
        """
        Embed query with caching.

        Args:
            text: Query text to embed

        Returns:
            Embedding vector
        """
        cached_embedding = self._cache.get_embedding(self.model, text)
        if cached_embedding is not None:
            logger.debug("Query embedding retrieved from cache")
            return cached_embedding

        logger.debug("Generating new query embedding (cache miss)")
        embedding = self._ollama_embeddings.embed_query(text)
        self._cache.set_embedding(self.model, text, embedding)

        return embedding


class OptimizedVectorDB:
    """
    Optimized vector database service with caching for models and instances.
    
    This service eliminates redundant embedding model initialization and provides
    efficient database instance management with thread-safe operations.
    """
    
    def __init__(self):
        """Initialize the OptimizedVectorDB service."""
        self._embedding_models: Dict[str, CachedOllamaEmbeddings] = {}
        self._db_instances: Dict[str, Chroma] = {}
        self._model_lock = threading.Lock()
        self._db_lock = threading.Lock()
        self._config_cache: Optional[Dict] = None

        logger.info("OptimizedVectorDB service initialized")
    
    def _load_embedding_config(self) -> Dict:
        """
        Load embedding configuration from config file.
        
        Returns:
            Embedding configuration dictionary
        """
        if self._config_cache is not None:
            return self._config_cache
        
        try:
            config_path = os.path.join(
                os.path.dirname(__file__), '../../config/default_models.json'
            )
            
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                embedding_params = config.get('embedding_parameters', {})
                self._config_cache = {
                    'embedding_prompt': embedding_params.get('embedding_prompt', ''),
                    'query_prompt': embedding_params.get('query_prompt', '')
                }
                
                logger.debug(f"Loaded embedding config: {self._config_cache}")
                return self._config_cache
                
        except Exception as e:
            logger.warning(f"Failed to load embedding config: {e}")
        
        # Default configuration
        self._config_cache = {
            'embedding_prompt': 'Represent this ERDB research document for retrieval: ',
            'query_prompt': 'Represent this question for retrieving ERDB research documents: '
        }
        
        return self._config_cache
    
    def _check_model_availability(self, model_name: str) -> bool:
        """
        Check if an embedding model is available in Ollama.
        
        Args:
            model_name: Name of the model to check
            
        Returns:
            True if model is available, False otherwise
        """
        try:
            response = requests.get(f"{OLLAMA_BASE_URL}/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get('models', [])
                available_models = [model['name'] for model in models]
                is_available = model_name in available_models
                
                logger.debug(f"Model {model_name} availability: {is_available}")
                return is_available
            else:
                logger.warning(f"Failed to check model availability: HTTP {response.status_code}")
                
        except Exception as e:
            logger.warning(f"Error checking model availability for {model_name}: {e}")
        
        return False
    
    def _get_available_embedding_model(self) -> str:
        """
        Get the first available embedding model from the fallback list.
        
        Returns:
            Name of available embedding model
            
        Raises:
            RuntimeError: If no embedding models are available
        """
        # Try primary model first
        if self._check_model_availability(TEXT_EMBEDDING_MODEL):
            logger.info(f"Using primary embedding model: {TEXT_EMBEDDING_MODEL}")
            return TEXT_EMBEDDING_MODEL
        
        # Try fallback models
        for model_name in FALLBACK_EMBEDDING_MODELS:
            if self._check_model_availability(model_name):
                logger.info(f"Using fallback embedding model: {model_name}")
                return model_name
        
        # If no models are available, use the primary model anyway
        # (it might become available or the check might have failed)
        logger.warning(f"No embedding models found available, using primary: {TEXT_EMBEDDING_MODEL}")
        return TEXT_EMBEDDING_MODEL
    
    def get_embedding_model(self, model_name: Optional[str] = None) -> CachedOllamaEmbeddings:
        """
        Get or create cached embedding model.
        
        Args:
            model_name: Specific model name to use. If None, uses best available.
            
        Returns:
            Cached CachedOllamaEmbeddings instance
            
        Raises:
            RuntimeError: If model initialization fails
        """
        # Determine which model to use
        if model_name is None:
            model_name = self._get_available_embedding_model()
        
        # Check if model is already cached
        if model_name in self._embedding_models:
            logger.debug(f"Using cached embedding model: {model_name}")
            return self._embedding_models[model_name]
        
        # Create new model instance with thread safety
        with self._model_lock:
            # Double-check pattern to avoid race conditions
            if model_name in self._embedding_models:
                return self._embedding_models[model_name]
            
            try:
                logger.info(f"Initializing embedding model: {model_name}")
                
                # Load configuration for prompts
                config = self._load_embedding_config()
                
                # Create the cached embedding model
                embedding_model = CachedOllamaEmbeddings(
                    model=model_name,
                    base_url=OLLAMA_BASE_URL
                )
                
                # Cache the model
                self._embedding_models[model_name] = embedding_model
                
                logger.info(f"Successfully initialized and cached embedding model: {model_name}")
                return embedding_model
                
            except Exception as e:
                logger.error(f"Failed to initialize embedding model {model_name}: {e}")
                
                # Try fallback models if primary model fails
                if model_name == TEXT_EMBEDDING_MODEL:
                    for fallback_model in FALLBACK_EMBEDDING_MODELS:
                        if fallback_model != model_name:
                            try:
                                logger.info(f"Trying fallback model: {fallback_model}")
                                return self.get_embedding_model(fallback_model)
                            except Exception as fallback_error:
                                logger.error(f"Fallback model {fallback_model} also failed: {fallback_error}")
                                continue
                
                raise RuntimeError(f"Could not initialize any embedding model. Last error: {e}")
    
    def get_vector_db(self, category: str, model_name: Optional[str] = None) -> Chroma:
        """
        Get optimized vector database instance.
        
        Args:
            category: Category for the vector database
            model_name: Specific embedding model to use
            
        Returns:
            Cached Chroma database instance
        """
        # Determine model name
        if model_name is None:
            model_name = self._get_available_embedding_model()
        
        # Create cache key
        cache_key = f"{category}_{model_name}"
        
        # Check if database instance is already cached
        if cache_key in self._db_instances:
            logger.debug(f"Using cached vector DB for key: {cache_key}")
            return self._db_instances[cache_key]
        
        # Create new database instance with thread safety
        with self._db_lock:
            # Double-check pattern to avoid race conditions
            if cache_key in self._db_instances:
                return self._db_instances[cache_key]
            
            try:
                logger.info(f"Creating vector database for category: {category}, model: {model_name}")
                
                # Get the embedding model
                embedding_model = self.get_embedding_model(model_name)
                
                # Create Chroma database instance
                db_instance = Chroma(
                    collection_name="unified_collection",
                    embedding_function=embedding_model,
                    persist_directory=CHROMA_PATH,
                    client_settings=Settings(
                        anonymized_telemetry=False,
                        allow_reset=True
                    )
                )
                
                # Cache the database instance
                self._db_instances[cache_key] = db_instance
                
                logger.info(f"Successfully created and cached vector DB for key: {cache_key}")
                return db_instance
                
            except Exception as e:
                logger.error(f"Failed to create vector database for {cache_key}: {e}")
                raise
    
    def similarity_search_with_category_filter(self, query: str, category: str, 
                                             k: int = 10, model_name: Optional[str] = None,
                                             **kwargs) -> List:
        """
        Perform similarity search with category filtering using optimized database.
        
        Args:
            query: Search query
            category: Category to filter by
            k: Number of results to return
            model_name: Specific embedding model to use
            **kwargs: Additional search parameters
            
        Returns:
            List of similar documents
        """
        try:
            db = self.get_vector_db(category, model_name)
            
            # Use metadata filtering for category separation
            filter_dict = {"category": category}
            
            results = db.similarity_search(
                query,
                k=k,
                filter=filter_dict,
                **kwargs
            )
            
            logger.info(f"Found {len(results)} documents for query in category {category}")
            return results
            
        except Exception as e:
            logger.error(f"Failed to perform similarity search: {str(e)}")
            raise
    
    def clear_cache(self, models_only: bool = False) -> None:
        """
        Clear cached models and/or database instances.
        
        Args:
            models_only: If True, only clear model cache. If False, clear both.
        """
        with self._model_lock:
            self._embedding_models.clear()
            logger.info("Cleared embedding model cache")
        
        if not models_only:
            with self._db_lock:
                self._db_instances.clear()
                logger.info("Cleared vector database instance cache")
        
        # Clear config cache
        self._config_cache = None
    
    def get_cache_stats(self) -> Dict:
        """
        Get cache statistics.
        
        Returns:
            Dictionary with cache statistics
        """
        return {
            'cached_models': list(self._embedding_models.keys()),
            'cached_db_instances': list(self._db_instances.keys()),
            'model_count': len(self._embedding_models),
            'db_instance_count': len(self._db_instances)
        }
    
    def reload_config(self) -> None:
        """Reload configuration and clear config cache."""
        self._config_cache = None
        logger.info("Configuration cache cleared, will reload on next access")


# Global instance for use throughout the application
optimized_vector_db = OptimizedVectorDB()


def get_optimized_vector_db() -> OptimizedVectorDB:
    """
    Get the global OptimizedVectorDB instance.
    
    Returns:
        Global OptimizedVectorDB instance
    """
    return optimized_vector_db


# Convenience functions for backward compatibility
def get_vector_db(category: str) -> Chroma:
    """
    Get vector database instance using optimized service.
    
    Args:
        category: Category for the vector database
        
    Returns:
        Chroma database instance
    """
    return optimized_vector_db.get_vector_db(category)


def similarity_search_with_category_filter(query: str, category: str, k: int = 10, **kwargs) -> List:
    """
    Perform similarity search with category filtering.
    
    Args:
        query: Search query
        category: Category to filter by
        k: Number of results to return
        **kwargs: Additional search parameters
        
    Returns:
        List of similar documents
    """
    return optimized_vector_db.similarity_search_with_category_filter(query, category, k, **kwargs)
