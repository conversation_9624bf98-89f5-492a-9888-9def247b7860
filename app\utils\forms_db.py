#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Database utilities for forms management
Handles CRUD operations for forms and form submissions
"""

import os
import sqlite3
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database path
DB_PATH = os.getenv("DB_PATH", "./erdb_main.db")

def get_db_connection():
    """Get database connection."""
    return sqlite3.connect(DB_PATH)

# Form Management Functions

def create_form(name: str, description: str, fields: List[Dict[str, Any]], is_active: bool = True) -> Optional[int]:
    """
    Create a new form.
    
    Args:
        name: Form name
        description: Form description
        fields: List of field definitions (JSON)
        is_active: Whether the form is active
        
    Returns:
        Form ID if successful, None otherwise
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        fields_json = json.dumps(fields)
        
        cursor.execute('''
            INSERT INTO forms (name, description, fields, is_active)
            VALUES (?, ?, ?, ?)
        ''', (name, description, fields_json, is_active))
        
        form_id = cursor.lastrowid
        conn.commit()
        
        logger.info(f"Created form '{name}' with ID {form_id}")
        return form_id
        
    except sqlite3.Error as e:
        logger.error(f"Error creating form: {str(e)}")
        if conn:
            conn.rollback()
        return None
    finally:
        if conn:
            conn.close()

def get_form(form_id: int) -> Optional[Dict[str, Any]]:
    """
    Get form by ID.
    
    Args:
        form_id: Form ID
        
    Returns:
        Form data if found, None otherwise
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, name, description, fields, is_active, created_at, updated_at
            FROM forms WHERE id = ?
        ''', (form_id,))
        
        row = cursor.fetchone()
        if row:
            return {
                'id': row[0],
                'name': row[1],
                'description': row[2],
                'fields': json.loads(row[3]),
                'is_active': bool(row[4]),
                'created_at': row[5],
                'updated_at': row[6]
            }
        return None
        
    except sqlite3.Error as e:
        logger.error(f"Error getting form: {str(e)}")
        return None
    finally:
        if conn:
            conn.close()

def get_all_forms() -> List[Dict[str, Any]]:
    """
    Get all forms.
    
    Returns:
        List of all forms
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, name, description, fields, is_active, created_at, updated_at
            FROM forms ORDER BY name
        ''')
        
        forms = []
        for row in cursor.fetchall():
            forms.append({
                'id': row[0],
                'name': row[1],
                'description': row[2],
                'fields': json.loads(row[3]),
                'is_active': bool(row[4]),
                'created_at': row[5],
                'updated_at': row[6]
            })
        
        return forms
        
    except sqlite3.Error as e:
        logger.error(f"Error getting all forms: {str(e)}")
        return []
    finally:
        if conn:
            conn.close()

def update_form(form_id: int, name: str, description: str, fields: List[Dict[str, Any]], is_active: bool) -> bool:
    """
    Update an existing form.
    
    Args:
        form_id: Form ID
        name: Form name
        description: Form description
        fields: List of field definitions
        is_active: Whether the form is active
        
    Returns:
        True if successful, False otherwise
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        fields_json = json.dumps(fields)
        
        cursor.execute('''
            UPDATE forms 
            SET name = ?, description = ?, fields = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (name, description, fields_json, is_active, form_id))
        
        if cursor.rowcount > 0:
            conn.commit()
            logger.info(f"Updated form {form_id}")
            return True
        else:
            logger.warning(f"Form {form_id} not found for update")
            return False
            
    except sqlite3.Error as e:
        logger.error(f"Error updating form: {str(e)}")
        if conn:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()

def delete_form(form_id: int) -> bool:
    """
    Delete a form.
    
    Args:
        form_id: Form ID
        
    Returns:
        True if successful, False otherwise
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('DELETE FROM forms WHERE id = ?', (form_id,))
        
        if cursor.rowcount > 0:
            conn.commit()
            logger.info(f"Deleted form {form_id}")
            return True
        else:
            logger.warning(f"Form {form_id} not found for deletion")
            return False
            
    except sqlite3.Error as e:
        logger.error(f"Error deleting form: {str(e)}")
        if conn:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()

# Form Submission Functions

def create_form_submission(form_id: int, pdf_document_id: int, submission_data: Dict[str, Any], 
                          user_id: Optional[int] = None, ip_address: Optional[str] = None, 
                          user_agent: Optional[str] = None) -> Optional[int]:
    """
    Create a new form submission.
    
    Args:
        form_id: Form ID
        pdf_document_id: PDF document ID
        submission_data: Form field values (JSON)
        user_id: User ID (optional)
        ip_address: IP address (optional)
        user_agent: User agent (optional)
        
    Returns:
        Submission ID if successful, None otherwise
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        submission_json = json.dumps(submission_data)
        
        cursor.execute('''
            INSERT INTO form_submissions (form_id, user_id, pdf_document_id, submission_data, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (form_id, user_id, pdf_document_id, submission_json, ip_address, user_agent))
        
        submission_id = cursor.lastrowid
        conn.commit()
        
        logger.info(f"Created form submission {submission_id} for PDF {pdf_document_id}")
        return submission_id
        
    except sqlite3.Error as e:
        logger.error(f"Error creating form submission: {str(e)}")
        if conn:
            conn.rollback()
        return None
    finally:
        if conn:
            conn.close()

def get_form_submission(submission_id: int) -> Optional[Dict[str, Any]]:
    """
    Get form submission by ID.
    
    Args:
        submission_id: Submission ID
        
    Returns:
        Submission data if found, None otherwise
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT fs.id, fs.form_id, fs.user_id, fs.pdf_document_id, fs.submission_data, 
                   fs.ip_address, fs.user_agent, fs.submitted_at,
                   f.name as form_name, pd.original_filename as pdf_filename
            FROM form_submissions fs
            JOIN forms f ON fs.form_id = f.id
            JOIN pdf_documents pd ON fs.pdf_document_id = pd.id
            WHERE fs.id = ?
        ''', (submission_id,))
        
        row = cursor.fetchone()
        if row:
            return {
                'id': row[0],
                'form_id': row[1],
                'user_id': row[2],
                'pdf_document_id': row[3],
                'submission_data': json.loads(row[4]),
                'ip_address': row[5],
                'user_agent': row[6],
                'submitted_at': row[7],
                'form_name': row[8],
                'pdf_filename': row[9]
            }
        return None
        
    except sqlite3.Error as e:
        logger.error(f"Error getting form submission: {str(e)}")
        return None
    finally:
        if conn:
            conn.close()

def get_form_submissions(form_id: Optional[int] = None, pdf_document_id: Optional[int] = None, 
                        limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
    """
    Get form submissions with optional filtering.
    
    Args:
        form_id: Filter by form ID (optional)
        pdf_document_id: Filter by PDF document ID (optional)
        limit: Maximum number of results
        offset: Number of results to skip
        
    Returns:
        List of form submissions
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        query = '''
            SELECT fs.id, fs.form_id, fs.user_id, fs.pdf_document_id, fs.submission_data, 
                   fs.ip_address, fs.user_agent, fs.submitted_at,
                   f.name as form_name, pd.original_filename as pdf_filename
            FROM form_submissions fs
            JOIN forms f ON fs.form_id = f.id
            JOIN pdf_documents pd ON fs.pdf_document_id = pd.id
        '''
        
        params = []
        where_clauses = []
        
        if form_id:
            where_clauses.append("fs.form_id = ?")
            params.append(form_id)
            
        if pdf_document_id:
            where_clauses.append("fs.pdf_document_id = ?")
            params.append(pdf_document_id)
            
        if where_clauses:
            query += " WHERE " + " AND ".join(where_clauses)
            
        query += " ORDER BY fs.submitted_at DESC LIMIT ? OFFSET ?"
        params.extend([limit, offset])
        
        cursor.execute(query, params)
        
        submissions = []
        for row in cursor.fetchall():
            submissions.append({
                'id': row[0],
                'form_id': row[1],
                'user_id': row[2],
                'pdf_document_id': row[3],
                'submission_data': json.loads(row[4]),
                'ip_address': row[5],
                'user_agent': row[6],
                'submitted_at': row[7],
                'form_name': row[8],
                'pdf_filename': row[9]
            })
        
        return submissions
        
    except sqlite3.Error as e:
        logger.error(f"Error getting form submissions: {str(e)}")
        return []
    finally:
        if conn:
            conn.close()

def delete_form_submission(submission_id: int) -> bool:
    """
    Delete a form submission.
    
    Args:
        submission_id: Submission ID
        
    Returns:
        True if successful, False otherwise
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('DELETE FROM form_submissions WHERE id = ?', (submission_id,))
        
        if cursor.rowcount > 0:
            conn.commit()
            logger.info(f"Deleted form submission {submission_id}")
            return True
        else:
            logger.warning(f"Form submission {submission_id} not found for deletion")
            return False
            
    except sqlite3.Error as e:
        logger.error(f"Error deleting form submission: {str(e)}")
        if conn:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()

# Category and PDF Document Functions

def get_category_form(category_name: str) -> Optional[Dict[str, Any]]:
    """
    Get the form associated with a category.
    
    Args:
        category_name: Category name
        
    Returns:
        Form data if found, None otherwise
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT f.id, f.name, f.description, f.fields, f.is_active
            FROM categories c
            JOIN forms f ON c.form_id = f.id
            WHERE c.name = ? AND f.is_active = 1
        ''', (category_name,))
        
        row = cursor.fetchone()
        if row:
            return {
                'id': row[0],
                'name': row[1],
                'description': row[2],
                'fields': json.loads(row[3]),
                'is_active': bool(row[4])
            }
        return None
        
    except sqlite3.Error as e:
        logger.error(f"Error getting category form: {str(e)}")
        return None
    finally:
        if conn:
            conn.close()

def link_form_to_category(category_name: str, form_id: int) -> bool:
    """
    Link a form to a category.
    
    Args:
        category_name: Category name
        form_id: Form ID
        
    Returns:
        True if successful, False otherwise
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE categories 
            SET form_id = ? 
            WHERE name = ?
        ''', (form_id, category_name))
        
        if cursor.rowcount > 0:
            conn.commit()
            logger.info(f"Linked form {form_id} to category {category_name}")
            return True
        else:
            logger.warning(f"Category {category_name} not found")
            return False
            
    except sqlite3.Error as e:
        logger.error(f"Error linking form to category: {str(e)}")
        if conn:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()

def get_pdf_document_form(pdf_document_id: int) -> Optional[Dict[str, Any]]:
    """
    Get the form required for a specific PDF document.
    
    Args:
        pdf_document_id: PDF document ID
        
    Returns:
        Form data if required, None otherwise
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT f.id, f.name, f.description, f.fields, f.is_active
            FROM pdf_documents pd
            JOIN forms f ON pd.form_id = f.id
            WHERE pd.id = ? AND f.is_active = 1
        ''', (pdf_document_id,))
        
        row = cursor.fetchone()
        if row:
            return {
                'id': row[0],
                'name': row[1],
                'description': row[2],
                'fields': json.loads(row[3]),
                'is_active': bool(row[4])
            }
        return None
        
    except sqlite3.Error as e:
        logger.error(f"Error getting PDF document form: {str(e)}")
        return None
    finally:
        if conn:
            conn.close()

def get_pdfs_by_form_id(form_id: int) -> List[Dict[str, Any]]:
    """
    Get all PDF documents that use a specific form for gated downloads.
    
    Args:
        form_id: Form ID
        
    Returns:
        List of PDF documents using this form
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT pd.id, pd.filename, pd.original_filename, pd.category, 
                   pd.upload_date, pd.file_size, pd.page_count,
                   COUNT(fs.id) as submission_count
            FROM pdf_documents pd
            LEFT JOIN form_submissions fs ON pd.id = fs.pdf_document_id
            WHERE pd.form_id = ?
            GROUP BY pd.id
            ORDER BY pd.upload_date DESC
        ''', (form_id,))
        
        pdfs = []
        for row in cursor.fetchall():
            pdfs.append({
                'id': row[0],
                'filename': row[1],
                'original_filename': row[2],
                'category': row[3],
                'upload_date': row[4],
                'file_size': row[5],
                'page_count': row[6],
                'submission_count': row[7]
            })
        
        return pdfs
        
    except sqlite3.Error as e:
        logger.error(f"Error getting PDFs by form ID: {str(e)}")
        return []
    finally:
        if conn:
            conn.close()

def get_form_usage_statistics(form_id: int) -> Dict[str, Any]:
    """
    Get usage statistics for a specific form.
    
    Args:
        form_id: Form ID
        
    Returns:
        Dictionary with form usage statistics
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Get total PDFs using this form
        cursor.execute('''
            SELECT COUNT(*) FROM pdf_documents WHERE form_id = ?
        ''', (form_id,))
        total_pdfs = cursor.fetchone()[0]
        
        # Get total submissions for this form
        cursor.execute('''
            SELECT COUNT(*) FROM form_submissions WHERE form_id = ?
        ''', (form_id,))
        total_submissions = cursor.fetchone()[0]
        
        # Get submissions by PDF
        cursor.execute('''
            SELECT pd.original_filename, COUNT(fs.id) as submission_count
            FROM pdf_documents pd
            LEFT JOIN form_submissions fs ON pd.id = fs.pdf_document_id
            WHERE pd.form_id = ?
            GROUP BY pd.id
            ORDER BY submission_count DESC
            LIMIT 5
        ''', (form_id,))
        
        top_pdfs = []
        for row in cursor.fetchall():
            top_pdfs.append({
                'filename': row[0],
                'submission_count': row[1]
            })
        
        # Get recent submissions (last 30 days)
        cursor.execute('''
            SELECT COUNT(*) FROM form_submissions 
            WHERE form_id = ? AND submitted_at >= datetime('now', '-30 days')
        ''', (form_id,))
        recent_submissions = cursor.fetchone()[0]
        
        return {
            'total_pdfs': total_pdfs,
            'total_submissions': total_submissions,
            'recent_submissions': recent_submissions,
            'top_pdfs': top_pdfs
        }
        
    except sqlite3.Error as e:
        logger.error(f"Error getting form usage statistics: {str(e)}")
        return {
            'total_pdfs': 0,
            'total_submissions': 0,
            'recent_submissions': 0,
            'top_pdfs': []
        }
    finally:
        if conn:
            conn.close()

def check_form_submission_exists(form_id: int, pdf_document_id: int, ip_address: str) -> bool:
    """
    Check if a form submission already exists for a given form, PDF, and IP address.
    
    Args:
        form_id: Form ID
        pdf_document_id: PDF document ID
        ip_address: IP address
        
    Returns:
        True if submission exists, False otherwise
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT COUNT(*) FROM form_submissions 
            WHERE form_id = ? AND pdf_document_id = ? AND ip_address = ?
        ''', (form_id, pdf_document_id, ip_address))
        
        count = cursor.fetchone()[0]
        return count > 0
        
    except sqlite3.Error as e:
        logger.error(f"Error checking form submission existence: {str(e)}")
        return False
    finally:
        if conn:
            conn.close() 