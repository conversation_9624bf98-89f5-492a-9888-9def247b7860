<table style="width:100%; border-collapse: collapse; margin-bottom: 1rem;" border="1" class="dataframe table table-sm table-bordered table-responsive">
  <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;"ead>
    <tr style="text-align: right;">
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;"></th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">0</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">1</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">2</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">3</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">0</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Age of Trees \nAge of Trees \n(vear) \n(year)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Kg. Tree/Year of Commercial Fertilizer \nKg. Tree/Year of Commercial Fertilizer</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">1</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">45-0-0 \n45-0-0</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">14-14-14 \n14-14-14</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">0-0-60 \n0-0-60</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">2</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">1 \n1</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">0.22 \n0.22</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">0.35 \n0.35</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">= \n-</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">3</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">2 \n2</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">0.44 \n0.44</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">0.70 \n0.70</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">- \n-</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">4</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">3 \n3</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">0.56 \n0.56</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">1.40 \n1.40</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">= \n-</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">5</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">4 \n4</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">- \n-</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">2.80 \n2.80</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">- \n-</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">6</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">5 \n5</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">- \n-</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">3.60 \n3.60</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">- \n-</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">7</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">6 \n6</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">= \n-</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">4.20 \n4.20</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">0.30 \n0.30</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">8</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">7 \n7</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">= \n-</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">5.70 \n5.70</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">0.30 \n0.30</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">9</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">8 \n8</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">= \n-</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">7.10 \n7.10</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">0.30 \n0.30</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">10</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">9 above \n9 above</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">- \n-</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">8.50 \n8.50</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">0.50 \n0.50</td>
    </tr>
  </tbody>
</table>