<table style="width:100%; border-collapse: collapse; margin-bottom: 1rem;" border="1" class="dataframe table table-sm table-bordered table-responsive">
  <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;"ead>
    <tr style="text-align: right;">
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;"></th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">0</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">1</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">0</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Components \nComponents</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Nutritional Value \nNutritional Value</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">1</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Moisture (%) \nMoisture (%)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">79.7 \n79.7</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">2</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Energy (calories) \nEnergy (calories)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">34.0 \n34.0</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">3</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Protein (g) \nProtein (g)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">0.6 \n0.6</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">4</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Fat (9) \nFat (g)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">1.0 \n1.0</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">5</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Carbohydrates (g) \nCarbohydrates (g)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">5.6 \n5.6</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">6</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Fiber () \nFiber (g)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">5.1 \n5.1</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">7</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Ash (g) \nAsh (g)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">0.1 \n0.1</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">8</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Calcium (mg) \nCalcium (mg)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">7.0 \n7.0</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">9</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Magnesium (mg) \nMagnesium (mg)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">13.0 \n13.0</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">10</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Phosphorous (mg) \nPhosphorous (mg)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">13.0 \n13.0</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">11</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Sodium (mg) \nSodium (mg)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">7.0 \n7.0</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">12</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Potassium (mg) \nPotassium (mg)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">45.0 \n45.0</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">13</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Iron (Q) \nIron (g)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">1.0 \n1.0</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">14</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Vitamin B1(g) \nVitamin B1(g)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">0.03 \n0.03</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">15</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Vitamin B2 (g) \nVitamin B2 (g)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">0.03 \n0.03</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">16</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Niacin (g) \nNiacin (g)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">0.3 \n0.3</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">17</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Ascorbic Acid (  g) \nAscorbic Acid ( g)</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">4.2 \n4.2</td>
    </tr>
  </tbody>
</table>